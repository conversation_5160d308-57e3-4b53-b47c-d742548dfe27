# Example Terraform variables file for Course Manager ECS deployment
# Copy this file to terraform.tfvars and customize the values

# AWS Configuration
aws_region = "us-west-2"
environment = "prod"
project_name = "gradeservice"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"
public_subnet_cidrs = ["********/24", "********/24"]
private_subnet_cidrs = ["*********/24", "*********/24"]

# External Database Configuration (managed outside Terraform)
db_host = "your-database-endpoint.region.rds.amazonaws.com"  # Change this!
db_port = 3306
db_name = "gradeservice"
db_username = "gradeservice_user"
db_password = "your-secure-database-password-here"  # Change this!

# ECS Configuration
app_cpu = 1024          # 1 vCPU
app_memory = 2048       # 2GB RAM
app_desired_count = 2   # Number of running tasks
app_min_capacity = 1    # Minimum tasks for auto scaling
app_max_capacity = 10   # Maximum tasks for auto scaling

# Application Configuration
app_port = 8080
health_check_path = "/gradeservice/"

# ECR Configuration
ecr_repository_name = "gradeservice"  # Will create: gradeservice-course-manager-repository
image_tag = "latest"    # Change to specific version for production

# Domain Configuration (optional)
# domain_name = "coursemanager.yourdomain.com"
# certificate_arn = "arn:aws:acm:us-west-2:123456789012:certificate/12345678-1234-1234-1234-123456789012"

# Monitoring Configuration
enable_cloudwatch_logs = true
log_retention_days = 7  # Increase for production (30, 90, etc.)


