# Variables for Course Manager ECS Deployment

variable "aws_deployment_region" {
  description = "AWS region for deployment"
  type        = string
  default     = "us-west-2"
}

variable "environment" {
  description = "Environment name (staging, production)"
  type        = string
  default     = "staging"
}

# VPC Configuration
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

# External Database Configuration
variable "db_secrets_manager_secret_name" {
  description = "Name of the Secrets Manager secret containing all database credentials and configuration (host, port, username, password, dbname)"
  type        = string
  default     = "course-manager/database/credentials"
}

# ECS Configuration
variable "app_cpu" {
  description = "CPU units for the application (1024 = 1 vCPU)"
  type        = number
  default     = 1024
}

variable "app_memory" {
  description = "Memory for the application in MB"
  type        = number
  default     = 2048
}

variable "app_desired_count" {
  description = "Desired number of application instances"
  type        = number
  default     = 2
}

variable "app_min_capacity" {
  description = "Minimum number of application instances"
  type        = number
  default     = 1
}

variable "app_max_capacity" {
  description = "Maximum number of application instances"
  type        = number
  default     = 10
}

# Application Configuration
variable "app_port" {
  description = "Port the application runs on"
  type        = number
  default     = 8080
}

variable "health_check_path" {
  description = "Health check path for the application"
  type        = string
  default     = "/gradeservice/"
}

# ECR Configuration
variable "ecr_repository_name" {
  description = "Name of the ECR repository (will be prefixed with project name)"
  type        = string
  default     = "gradeservice"
}

variable "image_tag" {
  description = "Docker image tag to deploy"
  type        = string
  default     = "latest"
}

# Domain Configuration (optional)
variable "domain_name" {
  description = "Domain name for the application (optional)"
  type        = string
  default     = ""
}

variable "course_manager_certificate_arn" {
  description = "ACM certificate ARN for HTTPS (optional)"
  type        = string
}

# Monitoring Configuration
variable "enable_cloudwatch_logs" {
  description = "Enable CloudWatch logs"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 7
}
