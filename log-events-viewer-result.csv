timestamp,message
1753045218877,🚀 Starting Course Manager Application...
1753045218878,📊 Environment: staging
1753045218878,🔍 Database Configuration:
1753045218878,   Host: staging-course-manager.cfyyzrxdmvqc.us-west-2.rds.amazonaws.com
1753045218878,   Port: 3306
1753045218878,   Database: gradeservice
1753045218878,   Username: gradeservice
1753045218878,   Password: [HIDDEN]
1753045218882,🔧 Configuring Tomcat server.xml with environment variables...
1753045218887,✅ Database URL configured as:
1753045218889,"url=""***********************************************************************************************************************************************************************************************"""
1753045218891,"ℹ️  MySQL client not available, skipping connectivity test"
1753045218891,🚀 Starting Tomcat...
1753045218923,NOTE: Picked up JDK_JAVA_OPTIONS:  --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
1753045221622,20-Jul-2025 21:00:21.613 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version name:   Apache Tomcat/9.0.65
1753045221623,20-Jul-2025 21:00:21.623 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server built:          Jul 14 2022 12:28:53 UTC
1753045221623,20-Jul-2025 21:00:21.623 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Server version number: ********
1753045221624,20-Jul-2025 21:00:21.624 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Name:               Linux
1753045221627,20-Jul-2025 21:00:21.627 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log OS Version:            5.10.238-231.953.amzn2.x86_64
1753045221627,20-Jul-2025 21:00:21.627 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Architecture:          amd64
1753045221628,20-Jul-2025 21:00:21.627 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Java Home:             /usr/local/openjdk-11
1753045221628,20-Jul-2025 21:00:21.628 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Version:           11.0.16+8
1753045221628,20-Jul-2025 21:00:21.628 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log JVM Vendor:            Oracle Corporation
1753045221628,20-Jul-2025 21:00:21.628 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_BASE:         /usr/local/tomcat
1753045221628,20-Jul-2025 21:00:21.628 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log CATALINA_HOME:         /usr/local/tomcat
1753045221713,20-Jul-2025 21:00:21.713 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.lang=ALL-UNNAMED
1753045221718,20-Jul-2025 21:00:21.718 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.io=ALL-UNNAMED
1753045221719,20-Jul-2025 21:00:21.719 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util=ALL-UNNAMED
1753045221720,20-Jul-2025 21:00:21.720 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.base/java.util.concurrent=ALL-UNNAMED
1753045221720,20-Jul-2025 21:00:21.720 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED
1753045221720,20-Jul-2025 21:00:21.720 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.config.file=/usr/local/tomcat/conf/logging.properties
1753045221721,20-Jul-2025 21:00:21.721 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager
1753045221721,20-Jul-2025 21:00:21.721 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.security.egd=file:/dev/./urandom
1753045221721,20-Jul-2025 21:00:21.721 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djdk.tls.ephemeralDHKeySize=2048
1753045221721,20-Jul-2025 21:00:21.721 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.protocol.handler.pkgs=org.apache.catalina.webresources
1753045221722,20-Jul-2025 21:00:21.721 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dorg.apache.catalina.security.SecurityListener.UMASK=0027
1753045221722,20-Jul-2025 21:00:21.722 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xmx1024m
1753045221722,20-Jul-2025 21:00:21.722 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Xms512m
1753045221722,20-Jul-2025 21:00:21.722 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dignore.endorsed.dirs=
1753045221723,20-Jul-2025 21:00:21.723 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.base=/usr/local/tomcat
1753045221723,20-Jul-2025 21:00:21.723 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Dcatalina.home=/usr/local/tomcat
1753045221723,20-Jul-2025 21:00:21.723 INFO [main] org.apache.catalina.startup.VersionLoggerListener.log Command line argument: -Djava.io.tmpdir=/usr/local/tomcat/temp
1753045221732,20-Jul-2025 21:00:21.732 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent Loaded Apache Tomcat Native library [1.2.35] using APR version [1.7.0].
1753045221733,"20-Jul-2025 21:00:21.733 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [true]."
1753045221733,"20-Jul-2025 21:00:21.733 INFO [main] org.apache.catalina.core.AprLifecycleListener.lifecycleEvent APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]"
1753045221737,20-Jul-2025 21:00:21.737 INFO [main] org.apache.catalina.core.AprLifecycleListener.initializeSSL OpenSSL successfully initialized [OpenSSL 1.1.1n  15 Mar 2022]
1753045223429,"20-Jul-2025 21:00:23.428 INFO [main] org.apache.coyote.AbstractProtocol.init Initializing ProtocolHandler [""http-nio-8080""]"
1753045223624,20-Jul-2025 21:00:23.624 INFO [main] org.apache.catalina.startup.Catalina.load Server initialization in [3497] milliseconds
1753045225330,20-Jul-2025 21:00:25.330 INFO [main] org.apache.catalina.core.StandardService.startInternal Starting service [Catalina]
1753045225331,20-Jul-2025 21:00:25.331 INFO [main] org.apache.catalina.core.StandardEngine.startInternal Starting Servlet engine: [Apache Tomcat/9.0.65]
1753045225341,20-Jul-2025 21:00:25.341 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deploying web application directory [/usr/local/tomcat/webapps/gradeservice]
1753045225526,20-Jul-2025 21:00:25.517 WARNING [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin Match [Context] failed to set property [antiJARLocking] to [true]
1753045225630,20-Jul-2025 21:00:25.629 WARNING [main] org.apache.tomcat.util.digester.SetPropertiesRule.begin Match [Context] failed to set property [antiJARLocking] to [true]
1753045233437,20-Jul-2025 21:00:33.437 INFO [main] org.apache.jasper.servlet.TldScanner.scanJars At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
1753045233711,"20-Jul-2025 21:00:33.632 INFO [main] org.apache.catalina.startup.HostConfig.deployDirectory Deployment of web application directory [/usr/local/tomcat/webapps/gradeservice] has finished in [8,290] ms"
1753045233714,"20-Jul-2025 21:00:33.714 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler [""http-nio-8080""]"
1753045233735,20-Jul-2025 21:00:33.735 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [10110] milliseconds
1753062582213,21-Jul-2025 01:49:42.211 INFO [http-nio-8080-exec-5] org.apache.coyote.http11.Http11Processor.service Error parsing HTTP request header
1753062582213, Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
1753062582213,	java.lang.IllegalArgumentException: Invalid character found in the request target [/index.php?s=/index/\think\app/invokefunction&function=call_user_func_array&vars[0]=md5&vars[1][]=Hello ]. The valid characters are defined in RFC 7230 and RFC 3986
1753062582213,		at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:494)
1753062582213,		at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:271)
1753062582213,		at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
1753062582213,		at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
1753062582213,		at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
1753062582213,		at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
1753062582213,		at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
1753062582213,		at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
1753062582213,		at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
1753062582213,		at java.base/java.lang.Thread.run(Thread.java:829)