timestamp,message
1752883898917,	at org.hibernate.cfg.Configuration.buildSessionFactory(Configuration.java:1755) ~[hibernate-core-4.2.3.Final.jar:4.2.3.Final]
1752883898917,	at gs.hinkleworld.persistence.Hibernate.initSessionFactory(Hibernate.java:46) ~[classes/:?]
1752883898917,	at gs.hinkleworld.core.AppModel.contextInitialized(AppModel.java:68) ~[classes/:?]
1752883898917,	at org.apache.catalina.core.StandardContext.listenerStart(StandardContext.java:4768) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5230) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.ContainerBase.addChildInternal(ContainerBase.java:726) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.ContainerBase.addChild(ContainerBase.java:698) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.StandardHost.addChild(StandardHost.java:696) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.startup.HostConfig.deployWAR(HostConfig.java:1024) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.startup.HostConfig$DeployWar.run(HostConfig.java:1911) ~[catalina.jar:9.0.65]
1752883898917,	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:515) ~[?:?]
1752883898917,	at java.util.concurrent.FutureTask.run(FutureTask.java:264) ~[?:?]
1752883898917,	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75) ~[tomcat-util.jar:9.0.65]
1752883898917,	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:118) ~[?:?]
1752883898917,	at org.apache.catalina.startup.HostConfig.deployWARs(HostConfig.java:825) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.startup.HostConfig.deployApps(HostConfig.java:475) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.startup.HostConfig.start(HostConfig.java:1618) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.startup.HostConfig.lifecycleEvent(HostConfig.java:319) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.fireLifecycleEvent(LifecycleBase.java:123) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.setStateInternal(LifecycleBase.java:423) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.setState(LifecycleBase.java:366) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:946) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:835) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1396) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1386) ~[catalina.jar:9.0.65]
1752883898917,	at java.util.concurrent.FutureTask.run(FutureTask.java:264) ~[?:?]
1752883898917,	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75) ~[tomcat-util.jar:9.0.65]
1752883898917,	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:140) ~[?:?]
1752883898917,	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:919) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:265) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:432) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:930) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.catalina.startup.Catalina.start(Catalina.java:772) ~[catalina.jar:9.0.65]
1752883898917,	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:?]
1752883898917,	at jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:?]
1752883898917,	at jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:?]
1752883898917,	at java.lang.reflect.Method.invoke(Method.java:566) ~[?:?]
1752883898917,	at org.apache.catalina.startup.Bootstrap.start(Bootstrap.java:345) ~[bootstrap.jar:9.0.65]
1752883898917,	at org.apache.catalina.startup.Bootstrap.main(Bootstrap.java:476) ~[bootstrap.jar:9.0.65]
1752883898917,Caused by: javax.naming.NamingException: Unexpected exception resolving reference
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:892) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:172) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.factory.ResourceLinkFactory.getObjectInstance(ResourceLinkFactory.java:152) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.spi.NamingManager.getObjectInstance(NamingManager.java:341) ~[?:?]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:864) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.SelectorContext.lookup(SelectorContext.java:138) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.InitialContext.lookup(InitialContext.java:413) ~[?:?]
1752883898917,	at org.hibernate.service.jndi.internal.JndiServiceImpl.locate(JndiServiceImpl.java:65) ~[hibernate-core-4.2.3.Final.jar:4.2.3.Final]
1752883898917,	... 54 more
1752883898917,Caused by: com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure
1752883898917,The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
1752883898917,	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:175) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:825) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at org.apache.tomcat.jdbc.pool.PooledConnection.connectUsingDriver(PooledConnection.java:347) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.PooledConnection.connect(PooledConnection.java:228) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.createConnection(ConnectionPool.java:769) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.borrowConnection(ConnectionPool.java:697) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.init(ConnectionPool.java:496) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.<init>(ConnectionPool.java:154) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceProxy.pCreatePool(DataSourceProxy.java:121) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceProxy.createPool(DataSourceProxy.java:110) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceFactory.createDataSource(DataSourceFactory.java:560) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceFactory.getObjectInstance(DataSourceFactory.java:244) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.naming.factory.FactoryBase.getObjectInstance(FactoryBase.java:96) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.spi.NamingManager.getObjectInstance(NamingManager.java:341) ~[?:?]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:864) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:172) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.factory.ResourceLinkFactory.getObjectInstance(ResourceLinkFactory.java:152) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.spi.NamingManager.getObjectInstance(NamingManager.java:341) ~[?:?]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:864) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.SelectorContext.lookup(SelectorContext.java:138) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.InitialContext.lookup(InitialContext.java:413) ~[?:?]
1752883898917,	at org.hibernate.service.jndi.internal.JndiServiceImpl.locate(JndiServiceImpl.java:65) ~[hibernate-core-4.2.3.Final.jar:4.2.3.Final]
1752883898917,	... 54 more
1752883898917,Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure
1752883898917,The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
1752883898917,	at jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[?:?]
1752883898917,	at jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[?:?]
1752883898917,	at jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[?:?]
1752883898917,	at java.lang.reflect.Constructor.newInstance(Constructor.java:490) ~[?:?]
1752883898917,	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:89) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at org.apache.tomcat.jdbc.pool.PooledConnection.connectUsingDriver(PooledConnection.java:347) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.PooledConnection.connect(PooledConnection.java:228) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.createConnection(ConnectionPool.java:769) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.borrowConnection(ConnectionPool.java:697) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.init(ConnectionPool.java:496) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.<init>(ConnectionPool.java:154) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceProxy.pCreatePool(DataSourceProxy.java:121) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceProxy.createPool(DataSourceProxy.java:110) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceFactory.createDataSource(DataSourceFactory.java:560) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceFactory.getObjectInstance(DataSourceFactory.java:244) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.naming.factory.FactoryBase.getObjectInstance(FactoryBase.java:96) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.spi.NamingManager.getObjectInstance(NamingManager.java:341) ~[?:?]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:864) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:172) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.factory.ResourceLinkFactory.getObjectInstance(ResourceLinkFactory.java:152) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.spi.NamingManager.getObjectInstance(NamingManager.java:341) ~[?:?]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:864) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.SelectorContext.lookup(SelectorContext.java:138) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.InitialContext.lookup(InitialContext.java:413) ~[?:?]
1752883898917,	at org.hibernate.service.jndi.internal.JndiServiceImpl.locate(JndiServiceImpl.java:65) ~[hibernate-core-4.2.3.Final.jar:4.2.3.Final]
1752883898917,	... 54 more
1752883898917,Caused by: java.net.UnknownHostException: mysql: Name or service not known
1752883898917,	at java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method) ~[?:?]
1752883898917,	at java.net.InetAddress$PlatformNameService.lookupAllHostAddr(InetAddress.java:929) ~[?:?]
1752883898917,	at java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1529) ~[?:?]
1752883898917,	at java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:848) ~[?:?]
1752883898917,	at java.net.InetAddress.getAllByName0(InetAddress.java:1519) ~[?:?]
1752883898917,	at java.net.InetAddress.getAllByName(InetAddress.java:1378) ~[?:?]
1752883898917,	at java.net.InetAddress.getAllByName(InetAddress.java:1306) ~[?:?]
1752883898917,	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:130) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.NativeSession.connect(NativeSession.java:121) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:945) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:815) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188) ~[mysql-connector-j.jar:8.0.33]
1752883898917,	at org.apache.tomcat.jdbc.pool.PooledConnection.connectUsingDriver(PooledConnection.java:347) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.PooledConnection.connect(PooledConnection.java:228) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.createConnection(ConnectionPool.java:769) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.borrowConnection(ConnectionPool.java:697) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.init(ConnectionPool.java:496) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.ConnectionPool.<init>(ConnectionPool.java:154) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceProxy.pCreatePool(DataSourceProxy.java:121) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceProxy.createPool(DataSourceProxy.java:110) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceFactory.createDataSource(DataSourceFactory.java:560) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.tomcat.jdbc.pool.DataSourceFactory.getObjectInstance(DataSourceFactory.java:244) ~[tomcat-jdbc.jar:?]
1752883898917,	at org.apache.naming.factory.FactoryBase.getObjectInstance(FactoryBase.java:96) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.spi.NamingManager.getObjectInstance(NamingManager.java:341) ~[?:?]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:864) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:172) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.factory.ResourceLinkFactory.getObjectInstance(ResourceLinkFactory.java:152) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.spi.NamingManager.getObjectInstance(NamingManager.java:341) ~[?:?]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:864) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:850) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.NamingContext.lookup(NamingContext.java:158) ~[catalina.jar:9.0.65]
1752883898917,	at org.apache.naming.SelectorContext.lookup(SelectorContext.java:138) ~[catalina.jar:9.0.65]
1752883898917,	at javax.naming.InitialContext.lookup(InitialContext.java:413) ~[?:?]
1752883898917,	at org.hibernate.service.jndi.internal.JndiServiceImpl.locate(JndiServiceImpl.java:65) ~[hibernate-core-4.2.3.Final.jar:4.2.3.Final]
1752883898917,	... 54 more
1752883898918,19-Jul-2025 00:11:38.918 SEVERE [main] org.apache.catalina.core.StandardContext.startInternal One or more listeners failed to start. Full details will be found in the appropriate container log file
1752883899000,19-Jul-2025 00:11:39.000 SEVERE [main] org.apache.catalina.core.StandardContext.startInternal Context [/gradeservice] startup failed due to previous errors
1752883899004,"2025-07-19 00:11:39,003 INFO g.h.c.AppModel [main] contextDestroyed"
1752883899105,"19-Jul-2025 00:11:39.105 INFO [main] org.apache.catalina.startup.HostConfig.deployWAR Deployment of web application archive [/usr/local/tomcat/webapps/gradeservice.war] has finished in [14,802] ms"
1752883899107,"19-Jul-2025 00:11:39.107 INFO [main] org.apache.coyote.AbstractProtocol.start Starting ProtocolHandler [""http-nio-8080""]"
1752883899205,19-Jul-2025 00:11:39.205 INFO [main] org.apache.catalina.startup.Catalina.start Server startup in [16279] milliseconds