# # Outputs for Course Manager ECS Deployment
# 
# # VPC Outputs
# output "vpc_id" {
#   description = "ID of the Course Manager VPC"
#   value       = aws_vpc.course_manager_vpc.id
# }
# 
# output "vpc_cidr_block" {
#   description = "CIDR block of the Course Manager VPC"
#   value       = aws_vpc.course_manager_vpc.cidr_block
# }
# 
# output "public_subnet_ids" {
#   description = "IDs of the Course Manager public subnets"
#   value       = aws_subnet.course_manager_public_subnets[*].id
# }
# 
# output "private_subnet_ids" {
#   description = "IDs of the Course Manager private subnets"
#   value       = aws_subnet.course_manager_private_subnets[*].id
# }
# 
# # ECR Outputs
# output "ecr_repository_url" {
#   description = "URL of the Course Manager ECR repository"
#   value       = aws_ecr_repository.course_manager_ecr_repository.repository_url
# }
# 
# output "ecr_repository_name" {
#   description = "Name of the Course Manager ECR repository"
#   value       = aws_ecr_repository.course_manager_ecr_repository.name
# }
# 
# # ECS Outputs
# output "ecs_cluster_id" {
#   description = "ID of the Course Manager ECS cluster"
#   value       = aws_ecs_cluster.course_manager_cluster.id
# }
# 
# output "ecs_cluster_name" {
#   description = "Name of the Course Manager ECS cluster"
#   value       = aws_ecs_cluster.course_manager_cluster.name
# }
# 
# output "ecs_service_name" {
#   description = "Name of the Course Manager ECS service"
#   value       = aws_ecs_service.course_manager_ecs_service.name
# }
# 
# output "ecs_task_definition_arn" {
#   description = "ARN of the Course Manager ECS task definition"
#   value       = aws_ecs_task_definition.course_manager_task_definition.arn
# }
# 
# # Load Balancer Outputs
# output "load_balancer_dns_name" {
#   description = "DNS name of the Course Manager load balancer"
#   value       = aws_lb.course_manager_alb.dns_name
# }
# 
# output "load_balancer_zone_id" {
#   description = "Zone ID of the Course Manager load balancer"
#   value       = aws_lb.course_manager_alb.zone_id
# }
# 
# output "load_balancer_arn" {
#   description = "ARN of the Course Manager load balancer"
#   value       = aws_lb.course_manager_alb.arn
# }
# 
# output "target_group_arn" {
#   description = "ARN of the Course Manager target group"
#   value       = aws_lb_target_group.course_manager_target_group.arn
# }
# 
# # Application URLs
# output "application_url" {
#   description = "URL to access the Course Manager application"
#   value       = var.certificate_arn != "" ? "https://${aws_lb.course_manager_alb.dns_name}/gradeservice/" : "http://${aws_lb.course_manager_alb.dns_name}/gradeservice/"
# }
# 
# output "application_domain" {
#   description = "Domain name for the Course Manager application"
#   value       = var.domain_name != "" ? var.domain_name : aws_lb.course_manager_alb.dns_name
# }
# 
# # Security Group Outputs
# output "alb_security_group_id" {
#   description = "ID of the Course Manager ALB security group"
#   value       = aws_security_group.course_manager_alb.id
# }
# 
# output "ecs_security_group_id" {
#   description = "ID of the Course Manager ECS tasks security group"
#   value       = aws_security_group.course_manager_ecs_tasks.id
# }
# 
# # CloudWatch Outputs
# output "cloudwatch_log_group_name" {
#   description = "Name of the Course Manager CloudWatch log group"
#   value       = aws_cloudwatch_log_group.course_manager_app_logs.name
# }
# 
# # SNS Outputs
# output "sns_alerts_topic_arn" {
#   description = "ARN of the Course Manager SNS alerts topic"
#   value       = aws_sns_topic.course_manager_alerts_topic.arn
# }
# 
# # S3 Outputs
# output "alb_logs_bucket_name" {
#   description = "Name of the S3 bucket for Course Manager ALB logs"
#   value       = aws_s3_bucket.course_manager_alb_logs.id
# }
# 
# # Auto Scaling Outputs
# output "autoscaling_target_resource_id" {
#   description = "Resource ID of the Course Manager autoscaling target"
#   value       = aws_appautoscaling_target.course_manager_autoscaling_target.resource_id
# }
# 
# # Deployment Information
# output "deployment_region" {
#   description = "AWS region where resources are deployed"
#   value       = var.aws_deployment_region
# }
# 
# output "deployment_environment" {
#   description = "Environment name"
#   value       = var.environment
# }