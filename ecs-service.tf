# ECS Service and Auto Scaling for Course Manager

# ECS Service for Course Manager
resource "aws_ecs_service" "course_manager_ecs_service" {
  name            = "${var.project_name}-course-manager-ecs-service-${terraform.workspace}"
  cluster         = aws_ecs_cluster.course_manager_cluster.id
  task_definition = aws_ecs_task_definition.course_manager_task_definition.arn
  desired_count   = var.app_desired_count
  launch_type     = "FARGATE"

  network_configuration {
    security_groups  = [aws_security_group.course_manager_ecs_tasks.id]
    subnets          = aws_subnet.course_manager_private_subnets[*].id
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.course_manager_target_group.arn
    container_name   = "${var.project_name}-course-manager-container-${terraform.workspace}"
    container_port   = var.app_port
  }

  # Enable ECS Exec for debugging
  enable_execute_command = true

  # Deployment configuration
  deployment_configuration {
    maximum_percent         = 200
    minimum_healthy_percent = 100
  }

  # Service discovery (optional)
  # service_registries {
  #   registry_arn = aws_service_discovery_service.app.arn
  # }

  depends_on = [
    aws_lb_listener.course_manager_http_listener,
    aws_lb_listener.course_manager_https_listener,
    aws_lb_listener.course_manager_http_only_listener
  ]

  tags = {
    Name        = "${var.project_name}-course-manager-ecs-service-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }

  lifecycle {
    ignore_changes = [task_definition]
  }
}

# Auto Scaling Target for Course Manager
resource "aws_appautoscaling_target" "course_manager_autoscaling_target" {
  max_capacity       = var.app_max_capacity
  min_capacity       = var.app_min_capacity
  resource_id        = "service/${aws_ecs_cluster.course_manager_cluster.name}/${aws_ecs_service.course_manager_ecs_service.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"

  tags = {
    Name        = "${var.project_name}-course-manager-autoscaling-target-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Auto Scaling Policy - CPU for Course Manager
resource "aws_appautoscaling_policy" "course_manager_cpu_autoscaling_policy" {
  name               = "${var.project_name}-course-manager-cpu-autoscaling-policy-${terraform.workspace}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.course_manager_autoscaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.course_manager_autoscaling_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.course_manager_autoscaling_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value       = 70.0
    scale_in_cooldown  = 300
    scale_out_cooldown = 300
  }
}

# Auto Scaling Policy - Memory for Course Manager
resource "aws_appautoscaling_policy" "course_manager_memory_autoscaling_policy" {
  name               = "${var.project_name}-course-manager-memory-autoscaling-policy-${terraform.workspace}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.course_manager_autoscaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.course_manager_autoscaling_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.course_manager_autoscaling_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageMemoryUtilization"
    }
    target_value       = 80.0
    scale_in_cooldown  = 300
    scale_out_cooldown = 300
  }
}

# Auto Scaling Policy - ALB Request Count for Course Manager
resource "aws_appautoscaling_policy" "course_manager_requests_autoscaling_policy" {
  name               = "${var.project_name}-course-manager-requests-autoscaling-policy-${terraform.workspace}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.course_manager_autoscaling_target.resource_id
  scalable_dimension = aws_appautoscaling_target.course_manager_autoscaling_target.scalable_dimension
  service_namespace  = aws_appautoscaling_target.course_manager_autoscaling_target.service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ALBRequestCountPerTarget"
      resource_label         = "${aws_lb.course_manager_alb.arn_suffix}/${aws_lb_target_group.course_manager_target_group.arn_suffix}"
    }
    target_value       = 1000.0
    scale_in_cooldown  = 300
    scale_out_cooldown = 300
  }
}

# CloudWatch Alarms for Course Manager monitoring
resource "aws_cloudwatch_metric_alarm" "course_manager_high_cpu_alarm" {
  alarm_name          = "${var.project_name}-course-manager-high-cpu-alarm-${terraform.workspace}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors Course Manager ECS CPU utilization"
  alarm_actions       = [aws_sns_topic.course_manager_alerts_topic.arn]

  dimensions = {
    ServiceName = aws_ecs_service.course_manager_ecs_service.name
    ClusterName = aws_ecs_cluster.course_manager_cluster.name
  }

  tags = {
    Name        = "${var.project_name}-course-manager-high-cpu-alarm-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_cloudwatch_metric_alarm" "course_manager_high_memory_alarm" {
  alarm_name          = "${var.project_name}-course-manager-high-memory-alarm-${terraform.workspace}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "85"
  alarm_description   = "This metric monitors Course Manager ECS memory utilization"
  alarm_actions       = [aws_sns_topic.course_manager_alerts_topic.arn]

  dimensions = {
    ServiceName = aws_ecs_service.course_manager_ecs_service.name
    ClusterName = aws_ecs_cluster.course_manager_cluster.name
  }

  tags = {
    Name        = "${var.project_name}-course-manager-high-memory-alarm-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# SNS Topic for Course Manager alerts
resource "aws_sns_topic" "course_manager_alerts_topic" {
  name = "${var.project_name}-course-manager-alerts-${terraform.workspace}"

  tags = {
    Name        = "${var.project_name}-course-manager-alerts-topic-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}
