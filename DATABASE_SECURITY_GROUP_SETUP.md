# Database Security Group Configuration

## Problem
Your ECS tasks can't connect to the external RDS database because the security groups are in different VPCs. The RDS security group needs to allow inbound connections from your ECS tasks.

## Solution Steps

### Step 1: Deploy Terraform to Get ECS Security Group ID

```bash
terraform plan -var-file="common-staging.tfvars" -var-file="secret-staging.tfvars" -out=terraform-plans/staging-plan.tfplan
terraform apply terraform-plans/staging-plan.tfplan
```

### Step 2: Get the ECS Security Group ID

```bash
terraform output ecs_security_group_id
```

This will output something like: `sg-0123456789abcdef0`

### Step 3: Add Security Group Rule to RDS

Replace `<ECS_SECURITY_GROUP_ID>` with the ID from Step 2:

```bash
aws ec2 authorize-security-group-ingress \
    --group-id sg-1a74ef7e \
    --protocol tcp \
    --port 3306 \
    --source-group <ECS_SECURITY_GROUP_ID> \
    --region us-west-2
```

### Step 4: Verify the Rule Was Added

```bash
aws ec2 describe-security-groups \
    --group-ids sg-1a74ef7e \
    --region us-west-2 \
    --query 'SecurityGroups[0].IpPermissions'
```

You should see a rule allowing TCP port 3306 from your ECS security group.

### Step 5: Test Database Connectivity

After adding the security group rule, your ECS tasks should be able to connect to the database. You can:

1. **Redeploy the ECS service** to test connectivity:
   ```bash
   aws ecs update-service \
       --cluster course-manager-ecs-cluster-staging \
       --service course-manager-ecs-service-staging \
       --force-new-deployment
   ```

2. **Monitor the logs** for successful database connections:
   ```bash
   aws logs tail /aws/ecs/course-manager-staging --follow
   ```

## Expected Results

After configuring the security group rule:
- ✅ ECS tasks can connect to RDS database
- ✅ Database connection errors should disappear
- ✅ Application should start successfully
- ✅ Health checks should pass

## Troubleshooting

If you still have connectivity issues after adding the security group rule:

1. **Check VPC connectivity**: Ensure your ECS VPC can route to your RDS VPC
2. **Verify database credentials**: Confirm the Secrets Manager secret has correct values
3. **Check database status**: Ensure the RDS instance is running and accessible
4. **Review network ACLs**: Check if network ACLs are blocking traffic

## Security Group Rule Details

The rule you're adding:
- **Type**: Inbound rule on RDS security group
- **Protocol**: TCP
- **Port**: 3306 (MySQL)
- **Source**: ECS tasks security group
- **Effect**: Allows ECS tasks to connect to RDS database

This is a secure configuration that only allows your specific ECS tasks to access the database, not all traffic.
