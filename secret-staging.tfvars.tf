# Workspace-specific variables for staging environment
# Copy this file to terraform-staging.tfvars and update with your actual values
# Add terraform-staging.tfvars to your .gitignore file

# External Database Configuration (managed outside Terraform)
db_secrets_manager_secret_name = "course-manager/database/credentials/staging"
db_host = "staging-database-endpoint.region.rds.amazonaws.com"
db_port = 3306
db_name = "gradeservice_staging"

# Environment Configuration
environment = "staging"
aws_region = "us-west-2"

# ECS Configuration
app_cpu = 512           # 0.5 vCPU for staging
app_memory = 1024       # 1GB RAM for staging
app_desired_count = 1   # Single instance for staging
app_min_capacity = 1
app_max_capacity = 2

# Application Configuration
app_port = 8080
health_check_path = "/health"
image_tag = "staging-latest"

# Logging Configuration
log_retention_days = 7  # Shorter retention for staging
