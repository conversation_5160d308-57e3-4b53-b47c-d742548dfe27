# External Database Configuration for Course Manager
# Note: RDS database is managed outside of Terraform

# Secrets Manager for Course Manager Database Credentials
resource "aws_secretsmanager_secret" "course_manager_db_credentials" {
  name        = "course-manager-database-credentials-${terraform.workspace}"
  description = "External database credentials for Course Manager ${terraform.workspace}"

  tags = {
    Name        = "course-manager-database-credentials-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_secretsmanager_secret_version" "course_manager_db_credentials_version" {
  secret_id = aws_secretsmanager_secret.course_manager_db_credentials.id
  secret_string = jsonencode({
    username = var.db_username
    password = var.db_password
    engine   = "mysql"
    host     = var.db_host
    port     = var.db_port
    dbname   = var.db_name
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}
