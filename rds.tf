# External Database Configuration for Course Manager
# Note: RDS database and credentials are managed outside of Terraform

# Data source to read existing Secrets Manager secret containing database credentials
data "aws_secretsmanager_secret" "course_manager_db_credentials" {
  name = var.db_secrets_manager_secret_name
}

data "aws_secretsmanager_secret_version" "course_manager_db_credentials_version" {
  secret_id = data.aws_secretsmanager_secret.course_manager_db_credentials.id
}
