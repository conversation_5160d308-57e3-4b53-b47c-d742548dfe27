#!/bin/bash

# <PERSON><PERSON>t to configure RDS security group to allow ECS task connections
# This needs to be run manually since the RDS is managed outside of Terraform

set -e

echo "🔧 Configuring RDS Security Group for ECS Access..."

# Configuration
RDS_SECURITY_GROUP_ID="sg-1a74ef7e"
AWS_REGION="us-west-2"

echo "📋 Step 1: Deploy Terraform to get ECS security group ID..."
echo "Run: terraform apply -var-file=\"common-staging.tfvars\" -var-file=\"secret-staging.tfvars\""
echo ""

echo "📋 Step 2: Get the ECS security group ID from Terraform output..."
echo "Run: terraform output ecs_security_group_id"
echo ""

echo "📋 Step 3: Add security group rule manually..."
echo "Replace <ECS_SECURITY_GROUP_ID> with the output from step 2:"
echo ""
echo "aws ec2 authorize-security-group-ingress \\"
echo "    --group-id ${RDS_SECURITY_GROUP_ID} \\"
echo "    --protocol tcp \\"
echo "    --port 3306 \\"
echo "    --source-group <ECS_SECURITY_GROUP_ID> \\"
echo "    --region ${AWS_REGION}"
echo ""

echo "📋 Step 4: Verify the rule was added..."
echo "aws ec2 describe-security-groups \\"
echo "    --group-ids ${RDS_SECURITY_GROUP_ID} \\"
echo "    --region ${AWS_REGION} \\"
echo "    --query 'SecurityGroups[0].IpPermissions'"
echo ""

echo "🎯 Alternative: If you know the ECS security group ID already..."
echo "You can run this command directly (replace <ECS_SECURITY_GROUP_ID>):"
echo ""
echo "aws ec2 authorize-security-group-ingress \\"
echo "    --group-id ${RDS_SECURITY_GROUP_ID} \\"
echo "    --protocol tcp \\"
echo "    --port 3306 \\"
echo "    --source-group <ECS_SECURITY_GROUP_ID> \\"
echo "    --region ${AWS_REGION}"
echo ""

echo "✅ After adding the rule, redeploy your ECS service to test connectivity."
