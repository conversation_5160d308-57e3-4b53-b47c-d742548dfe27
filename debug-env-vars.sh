#!/bin/bash

# Debug script to check environment variables in ECS container
echo "=== Environment Variables ==="
echo "DB_HOST: $DB_HOST"
echo "DB_PORT: $DB_PORT" 
echo "DB_NAME: $DB_NAME"
echo "DB_USERNAME: $DB_USERNAME"
echo "DB_PASSWORD: [REDACTED]"
echo "ENVIRONMENT: $ENVIRONMENT"
echo ""

echo "=== All Environment Variables ==="
env | grep -E "(DB_|ENVIRONMENT)" | sort
echo ""

echo "=== Secrets Manager Test ==="
# Test if we can resolve the secret
if command -v aws &> /dev/null; then
    echo "AWS CLI available, testing secret access..."
    aws secretsmanager get-secret-value --secret-id "course-manager-database-staging" --region us-west-2 --query SecretString --output text 2>/dev/null || echo "Cannot access secret"
else
    echo "AWS CLI not available in container"
fi

echo ""
echo "=== Network Connectivity Test ==="
if [ ! -z "$DB_HOST" ]; then
    echo "Testing connectivity to DB_HOST: $DB_HOST"
    nc -zv "$DB_HOST" "$DB_PORT" 2>&1 || echo "Cannot connect to database"
else
    echo "DB_HOST is not set!"
fi

echo ""
echo "=== DNS Resolution Test ==="
echo "Testing DNS resolution for 'mysql':"
nslookup mysql 2>&1 || echo "Cannot resolve 'mysql' hostname"

if [ ! -z "$DB_HOST" ]; then
    echo "Testing DNS resolution for DB_HOST: $DB_HOST"
    nslookup "$DB_HOST" 2>&1 || echo "Cannot resolve DB_HOST"
fi
