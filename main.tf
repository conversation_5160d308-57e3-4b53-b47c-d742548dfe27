# Course Manager ECS Deployment
# Main Terraform configuration

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# VPC for Course Manager Infrastructure
resource "aws_vpc" "course_manager_vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-vpc"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Internet Gateway for Course Manager VPC
resource "aws_internet_gateway" "course_manager_internet_gateway" {
  vpc_id = aws_vpc.course_manager_vpc.id

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-internet-gateway"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Public Subnets for Course Manager Infrastructure
resource "aws_subnet" "course_manager_public_subnets" {
  count = 2

  vpc_id                  = aws_vpc.course_manager_vpc.id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = data.aws_availability_zones.available.names[count.index]
  map_public_ip_on_launch = true

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-public-subnet-${count.index + 1}"
    Environment = var.environment
    Service     = "course-manager"
    Type        = "Public"
  }
}

# Private Subnets for Course Manager Infrastructure
resource "aws_subnet" "course_manager_private_subnets" {
  count = 2

  vpc_id            = aws_vpc.course_manager_vpc.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-private-subnet-${count.index + 1}"
    Environment = var.environment
    Service     = "course-manager"
    Type        = "Private"
  }
}

# Route Table for Course Manager Public Subnets
resource "aws_route_table" "course_manager_public_route_table" {
  vpc_id = aws_vpc.course_manager_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.course_manager_internet_gateway.id
  }

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-public-route-table"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Route Table Associations for Course Manager Public Subnets
resource "aws_route_table_association" "course_manager_public_route_table_associations" {
  count = 2

  subnet_id      = aws_subnet.course_manager_public_subnets[count.index].id
  route_table_id = aws_route_table.course_manager_public_route_table.id
}

# NAT Gateway for Course Manager Infrastructure
resource "aws_eip" "course_manager_nat_gateway_eips" {
  count = 2

  domain     = "vpc"
  depends_on = [aws_internet_gateway.course_manager_internet_gateway]

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-nat-gateway-eip-${count.index + 1}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_nat_gateway" "course_manager_nat_gateways" {
  count = 2

  allocation_id = aws_eip.course_manager_nat_gateway_eips[count.index].id
  subnet_id     = aws_subnet.course_manager_public_subnets[count.index].id

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-nat-gateway-${count.index + 1}"
    Environment = var.environment
    Service     = "course-manager"
  }

  depends_on = [aws_internet_gateway.course_manager_internet_gateway]
}

# Route Table for Course Manager Private Subnets
resource "aws_route_table" "course_manager_private_route_tables" {
  count = 2

  vpc_id = aws_vpc.course_manager_vpc.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.course_manager_nat_gateways[count.index].id
  }

  tags = {
    Name        = "${var.project_name}-${terraform.workspace}-course-manager-private-route-table-${count.index + 1}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Route Table Associations for Course Manager Private Subnets
resource "aws_route_table_association" "course_manager_private_route_table_associations" {
  count = 2

  subnet_id      = aws_subnet.course_manager_private_subnets[count.index].id
  route_table_id = aws_route_table.course_manager_private_route_tables[count.index].id
}
