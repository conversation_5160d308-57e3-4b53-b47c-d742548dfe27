# ECS Cluster and Service for Course Manager

# ECS Cluster for Course Manager
resource "aws_ecs_cluster" "course_manager_cluster" {
  name = "course-manager-ecs-cluster-${terraform.workspace}"

  configuration {
    execute_command_configuration {
      logging = "OVERRIDE"
      log_configuration {
        cloud_watch_log_group_name = aws_cloudwatch_log_group.course_manager_ecs_exec_logs.name
      }
    }
  }

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = {
    Name        = "course-manager-ecs-cluster-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# ECS Cluster Capacity Providers for Course Manager
resource "aws_ecs_cluster_capacity_providers" "course_manager_capacity_providers" {
  cluster_name = aws_ecs_cluster.course_manager_cluster.name

  capacity_providers = ["FARGATE", "FARGATE_SPOT"]

  default_capacity_provider_strategy {
    base              = 1
    weight            = 100
    capacity_provider = "FARGATE"
  }
}

# CloudWatch Log Group for Course Manager Application
resource "aws_cloudwatch_log_group" "course_manager_app_logs" {
  name              = "/aws/ecs/course-manager-${terraform.workspace}"
  retention_in_days = var.log_retention_days

  tags = {
    Name        = "course-manager-application-logs-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# CloudWatch Log Group for Course Manager ECS Exec
resource "aws_cloudwatch_log_group" "course_manager_ecs_exec_logs" {
  name              = "/aws/ecs/course-manager-${terraform.workspace}/exec"
  retention_in_days = var.log_retention_days

  tags = {
    Name        = "course-manager-ecs-exec-logs-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Security Group for Course Manager ECS Tasks
resource "aws_security_group" "course_manager_ecs_tasks" {
  name_prefix = "course-manager-ecs-tasks-${terraform.workspace}-"
  vpc_id      = aws_vpc.course_manager_vpc.id

  ingress {
    description     = "HTTP from Course Manager ALB"
    from_port       = var.app_port
    to_port         = var.app_port
    protocol        = "tcp"
    security_groups = [aws_security_group.course_manager_alb.id]
  }

  egress {
    description = "All outbound traffic for Course Manager"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "course-manager-ecs-tasks-sg-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# IAM Role for Course Manager ECS Task Execution
resource "aws_iam_role" "course_manager_ecs_task_execution_role" {
  name = "course-manager-ecs-task-execution-role-${terraform.workspace}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "course-manager-ecs-task-execution-role-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

resource "aws_iam_role_policy_attachment" "course_manager_ecs_task_execution_policy" {
  role       = aws_iam_role.course_manager_ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Additional policy for Course Manager Secrets Manager access
resource "aws_iam_role_policy" "course_manager_ecs_task_execution_secrets_policy" {
  name = "course-manager-ecs-task-execution-secrets-policy-${terraform.workspace}"
  role = aws_iam_role.course_manager_ecs_task_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = [
          data.aws_secretsmanager_secret.course_manager_db_credentials.arn
        ]
      }
    ]
  })
}

# IAM Role for Course Manager ECS Task
resource "aws_iam_role" "course_manager_ecs_task_role" {
  name = "course-manager-ecs-task-role-${terraform.workspace}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "course-manager-ecs-task-role-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}

# Policy for Course Manager ECS Exec
resource "aws_iam_role_policy" "course_manager_ecs_task_exec_policy" {
  name = "course-manager-ecs-task-exec-policy-${terraform.workspace}"
  role = aws_iam_role.course_manager_ecs_task_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssmmessages:CreateControlChannel",
          "ssmmessages:CreateDataChannel",
          "ssmmessages:OpenControlChannel",
          "ssmmessages:OpenDataChannel"
        ]
        Resource = "*"
      }
    ]
  })
}

# ECS Task Definition for Course Manager
resource "aws_ecs_task_definition" "course_manager_task_definition" {
  family                   = "course-manager-task-${terraform.workspace}"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.app_cpu
  memory                   = var.app_memory
  execution_role_arn       = aws_iam_role.course_manager_ecs_task_execution_role.arn
  task_role_arn            = aws_iam_role.course_manager_ecs_task_role.arn

  # Use ARM64 (Graviton) to match Apple Silicon builds
  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = "ARM64"
  }

  container_definitions = jsonencode([
    {
      name  = "course-manager-${terraform.workspace}"
      image = "${aws_ecr_repository.course_manager_ecr_repository.repository_url}:${var.image_tag}"

      portMappings = [
        {
          containerPort = var.app_port
          protocol      = "tcp"
        }
      ]

      environment = [
        {
          name  = "ENVIRONMENT"
          value = var.environment
        },
        {
          name  = "DB_HOST"
          value = "${data.aws_secretsmanager_secret.course_manager_db_credentials.arn}:host::"
        },
        {
          name  = "DB_PORT"
          value = tostring("${data.aws_secretsmanager_secret.course_manager_db_credentials.arn}:port::")
        },
        {
          name  = "DB_NAME"
          value = "${data.aws_secretsmanager_secret.course_manager_db_credentials.arn}:db_name::"
        }
      ]

      secrets = [
        {
          name      = "DB_USERNAME"
          valueFrom = "${data.aws_secretsmanager_secret.course_manager_db_credentials.arn}:username::"
        },
        {
          name      = "DB_PASSWORD"
          valueFrom = "${data.aws_secretsmanager_secret.course_manager_db_credentials.arn}:password::"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.course_manager_app_logs.name
          "awslogs-region"        = var.aws_deployment_region
          "awslogs-stream-prefix" = "course-manager"
        }
      }

      healthCheck = {
        command = [
          "CMD-SHELL",
          "curl -f http://localhost:${var.app_port}${var.health_check_path} || exit 1"
        ]
        interval    = 30
        timeout     = 5
        retries     = 3
        startPeriod = 60
      }

      essential = true
    }
  ])

  tags = {
    Name        = "course-manager-task-definition-${terraform.workspace}"
    Environment = var.environment
    Service     = "course-manager"
  }
}
