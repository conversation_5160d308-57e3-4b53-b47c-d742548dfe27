
Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  + create
  ~ update in-place
-/+ destroy and then create replacement

<PERSON><PERSON> will perform the following actions:

  # aws_ecs_service.course_manager_ecs_service will be updated in-place
  ~ resource "aws_ecs_service" "course_manager_ecs_service" {
        id                                 = "arn:aws:ecs:us-west-2:575979591116:service/course-manager-ecs-cluster-staging/course-manager-ecs-service-staging"
        name                               = "course-manager-ecs-service-staging"
        tags                               = {
            "Environment" = "staging"
            "Name"        = "course-manager-ecs-service-staging"
            "Service"     = "course-manager"
        }
      ~ task_definition                    = "arn:aws:ecs:us-west-2:575979591116:task-definition/course-manager-task-staging:7" -> (known after apply)
        # (16 unchanged attributes hidden)

        # (4 unchanged blocks hidden)
    }

  # aws_ecs_task_definition.course_manager_task_definition must be replaced
-/+ resource "aws_ecs_task_definition" "course_manager_task_definition" {
      ~ arn                      = "arn:aws:ecs:us-west-2:575979591116:task-definition/course-manager-task-staging:7" -> (known after apply)
      ~ arn_without_revision     = "arn:aws:ecs:us-west-2:575979591116:task-definition/course-manager-task-staging" -> (known after apply)
      ~ container_definitions    = (sensitive value) # forces replacement
      ~ enable_fault_injection   = false -> (known after apply)
      ~ id                       = "course-manager-task-staging" -> (known after apply)
      ~ revision                 = 7 -> (known after apply)
        tags                     = {
            "Environment" = "staging"
            "Name"        = "course-manager-task-definition-staging"
            "Service"     = "course-manager"
        }
        # (12 unchanged attributes hidden)
    }

  # aws_security_group_rule.ecs_to_rds will be created
  + resource "aws_security_group_rule" "ecs_to_rds" {
      + description              = "Allow ECS tasks to connect to external RDS database"
      + from_port                = 3306
      + id                       = (known after apply)
      + protocol                 = "tcp"
      + security_group_id        = "sg-0897aa81851766c8b"
      + security_group_rule_id   = (known after apply)
      + self                     = false
      + source_security_group_id = "sg-0c6742c33b03808fd"
      + to_port                  = 3306
      + type                     = "ingress"
    }

Plan: 2 to add, 1 to change, 1 to destroy.
