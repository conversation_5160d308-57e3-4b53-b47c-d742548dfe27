
Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  ~ update in-place
-/+ destroy and then create replacement

Terra<PERSON> will perform the following actions:

  # aws_ecs_service.course_manager_ecs_service will be updated in-place
  ~ resource "aws_ecs_service" "course_manager_ecs_service" {
        id                                 = "arn:aws:ecs:us-west-2:575979591116:service/course-manager-ecs-cluster-staging/course-manager-ecs-service-staging"
        name                               = "course-manager-ecs-service-staging"
        tags                               = {
            "Environment" = "staging"
            "Name"        = "course-manager-ecs-service-staging"
            "Service"     = "course-manager"
        }
      ~ task_definition                    = "arn:aws:ecs:us-west-2:575979591116:task-definition/course-manager-task-staging:6" -> (known after apply)
        # (16 unchanged attributes hidden)

        # (4 unchanged blocks hidden)
    }

  # aws_ecs_task_definition.course_manager_task_definition must be replaced
-/+ resource "aws_ecs_task_definition" "course_manager_task_definition" {
      ~ arn                      = "arn:aws:ecs:us-west-2:575979591116:task-definition/course-manager-task-staging:6" -> (known after apply)
      ~ arn_without_revision     = "arn:aws:ecs:us-west-2:575979591116:task-definition/course-manager-task-staging" -> (known after apply)
      ~ container_definitions    = (sensitive value) # forces replacement
      ~ enable_fault_injection   = false -> (known after apply)
      ~ id                       = "course-manager-task-staging" -> (known after apply)
      ~ revision                 = 6 -> (known after apply)
        tags                     = {
            "Environment" = "staging"
            "Name"        = "course-manager-task-definition-staging"
            "Service"     = "course-manager"
        }
        # (12 unchanged attributes hidden)
    }

Plan: 1 to add, 1 to change, 1 to destroy.
