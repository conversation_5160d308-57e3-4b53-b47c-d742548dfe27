
Terraform used the selected providers to generate the following execution
plan. Resource actions are indicated with the following symbols:
  + create

Terraform will perform the following actions:

  # aws_security_group_rule.ecs_to_rds will be created
  + resource "aws_security_group_rule" "ecs_to_rds" {
      + description              = "Allow ECS tasks to connect to external RDS database"
      + from_port                = 3306
      + id                       = (known after apply)
      + protocol                 = "tcp"
      + security_group_id        = "sg-1a74ef7e"
      + security_group_rule_id   = (known after apply)
      + self                     = false
      + source_security_group_id = "sg-0c6742c33b03808fd"
      + to_port                  = 3306
      + type                     = "ingress"
    }

Plan: 1 to add, 0 to change, 0 to destroy.
