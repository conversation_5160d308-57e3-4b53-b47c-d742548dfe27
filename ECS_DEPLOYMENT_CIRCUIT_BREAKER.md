# ECS Deployment Circuit Breaker Configuration

## Changes Made

I've configured the ECS service to stop retrying failed deployments after a certain number of failures using AWS ECS deployment circuit breaker.

### 1. ECS Service Configuration (`ecs-service.tf`)

**Added deployment circuit breaker:**
```hcl
deployment_configuration {
  maximum_percent         = 200
  minimum_healthy_percent = 100
  
  deployment_circuit_breaker {
    enable   = true
    rollback = true
  }
}
```

**Added health check grace period:**
```hcl
health_check_grace_period_seconds = 60
```

### 2. ALB Target Group Configuration (`alb.tf`)

**Updated health check settings for faster failure detection:**
```hcl
health_check {
  enabled             = true
  healthy_threshold   = 2
  interval            = 15        # Reduced from 30 seconds
  matcher             = "200"
  path                = var.health_check_path
  port                = "traffic-port"
  protocol            = "HTTP"
  timeout             = 5
  unhealthy_threshold = 3         # Increased from 2
}
```

## How It Works

### Deployment Circuit Breaker
- **`enable = true`**: Activates the circuit breaker
- **`rollback = true`**: Automatically rolls back to the previous stable task definition when deployment fails

### Circuit Breaker Behavior
The circuit breaker will trigger when:
1. **Task failures**: Tasks repeatedly fail to start or pass health checks
2. **Health check failures**: Tasks fail ALB health checks consistently
3. **Deployment timeout**: Deployment takes too long to stabilize

### Failure Detection Timeline
With the new configuration:
1. **Health check grace period**: 60 seconds before health checks start
2. **ALB health check interval**: Every 15 seconds
3. **Unhealthy threshold**: 3 consecutive failures = unhealthy
4. **Total time to mark unhealthy**: ~60 + (15 × 3) = ~105 seconds

### Circuit Breaker Triggers
The circuit breaker typically triggers after:
- **2-3 consecutive task failures**
- **Multiple health check failures across tasks**
- **Deployment doesn't stabilize within the expected timeframe**

## Benefits

1. **Faster Failure Detection**: Reduced health check interval from 30s to 15s
2. **Automatic Rollback**: Returns to previous working version automatically
3. **Prevents Infinite Retries**: Stops wasting resources on failing deployments
4. **Improved Stability**: Maintains service availability during failed deployments

## Deployment Commands

Apply the changes:
```bash
terraform plan -var-file="common-staging.tfvars" -var-file="secret-staging.tfvars" -out=terraform-plans/staging-plan.tfplan
terraform apply terraform-plans/staging-plan.tfplan
```

## Monitoring

After deployment, you can monitor circuit breaker events:

```bash
# Check service events
aws ecs describe-services \
    --cluster course-manager-ecs-cluster-staging \
    --services course-manager-ecs-service-staging \
    --query 'services[0].events[0:10]'

# Check deployment status
aws ecs describe-services \
    --cluster course-manager-ecs-cluster-staging \
    --services course-manager-ecs-service-staging \
    --query 'services[0].deployments'
```

## What Happens Next

When you deploy your next update:
1. If containers fail to start or pass health checks 2-3 times
2. The circuit breaker will trigger
3. ECS will automatically roll back to the previous task definition
4. The service will return to a stable state
5. You'll see circuit breaker events in the ECS console

This prevents the endless retry loop you were experiencing and gives you faster feedback when deployments fail.
